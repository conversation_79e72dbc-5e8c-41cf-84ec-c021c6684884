#!/bin/bash

# Set environment variables for OpenChoreo integration
export OPENCHOREO_API_URL=http://localhost:8080
export OPENCHOREO_TOKEN=""  # Optional, leave empty for now
export GITHUB_TOKEN=""  # Add your GitHub token here if needed

echo "Starting Backstage with OpenChoreo integration..."
echo "OpenChoreo API URL: $OPENCHOREO_API_URL"
echo "Make sure OpenChoreo API is running and accessible at $OPENCHOREO_API_URL"

# Start Backstage
yarn start
